"""
Test suite for database pool improvements

This test validates the refactored pool management system including:
- Elimination of excessive _ensure_pool calls
- Proper pool initialization
- Connection efficiency patterns
- Pool health monitoring
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from erp.database.connection.manager import DatabaseManager
from erp.database.connection.pool_manager import GlobalPoolManager, get_global_pool_manager
from erp.database.monitoring.pool_monitor import PoolMonitor, get_pool_monitor
from erp.database.registry.database_registry import DatabaseRegistry


class TestPoolInitialization:
    """Test pool initialization improvements"""

    @pytest.fixture
    def mock_pool(self):
        """Mock connection pool"""
        pool = MagicMock()
        pool._pool = MagicMock()
        pool._pool._minsize = 2
        pool._pool._maxsize = 5
        pool._pool.get_size.return_value = 3
        pool.is_closed = False
        return pool

    @pytest.fixture
    def db_manager(self):
        """Database manager instance"""
        return DatabaseManager("test_db")

    def test_initialization_state_tracking(self, db_manager):
        """Test that initialization state is properly tracked"""
        assert not db_manager._pool_initialized
        assert db_manager._pool is None

    @pytest.mark.asyncio
    async def test_ensure_initialized_once(self, db_manager, mock_pool):
        """Test that pool is only initialized once"""
        with patch.object(db_manager, '_pool', mock_pool):
            with patch('erp.database.connection.manager.get_global_pool_manager') as mock_manager:
                mock_global_manager = AsyncMock()
                mock_global_manager.get_pool.return_value = mock_pool
                mock_manager.return_value = mock_global_manager

                # First call should initialize
                await db_manager.ensure_initialized()
                assert db_manager._pool_initialized
                assert mock_global_manager.get_pool.call_count == 1

                # Second call should not initialize again
                await db_manager.ensure_initialized()
                assert mock_global_manager.get_pool.call_count == 1

    def test_check_initialized_raises_error(self, db_manager):
        """Test that operations fail when not initialized"""
        with pytest.raises(RuntimeError, match="not initialized"):
            db_manager._check_initialized()

    @pytest.mark.asyncio
    async def test_operations_require_initialization(self, db_manager):
        """Test that database operations require initialization"""
        with pytest.raises(RuntimeError, match="not initialized"):
            await db_manager.execute("SELECT 1")

        with pytest.raises(RuntimeError, match="not initialized"):
            await db_manager.fetch("SELECT 1")


class TestConnectionEfficiency:
    """Test connection efficiency patterns"""

    @pytest.fixture
    def initialized_manager(self, mock_pool):
        """Initialized database manager"""
        manager = DatabaseManager("test_db")
        manager._pool = mock_pool
        manager._pool_initialized = True
        manager.crud = MagicMock()
        return manager

    @pytest.mark.asyncio
    async def test_batch_operations(self, initialized_manager):
        """Test batch operations use single connection"""
        mock_conn = AsyncMock()
        mock_conn.execute.return_value = "OK"
        
        initialized_manager._pool.acquire_connection.return_value.__aenter__.return_value = mock_conn
        initialized_manager._pool.acquire_connection.return_value.__aexit__.return_value = None

        queries = ["INSERT INTO test VALUES (1)", "INSERT INTO test VALUES (2)"]
        results = await initialized_manager.batch_execute(queries)

        assert len(results) == 2
        assert all(result == "OK" for result in results)
        # Verify single connection was used
        assert initialized_manager._pool.acquire_connection.call_count == 1

    @pytest.mark.asyncio
    async def test_transaction_context(self, initialized_manager):
        """Test transaction context manager"""
        mock_conn = AsyncMock()
        mock_transaction = AsyncMock()
        mock_conn.transaction.return_value = mock_transaction
        
        initialized_manager._pool.acquire_connection.return_value.__aenter__.return_value = mock_conn
        initialized_manager._pool.acquire_connection.return_value.__aexit__.return_value = None

        async with initialized_manager.transaction_context() as tx_manager:
            assert tx_manager._shared_connection == mock_conn

        # Verify shared connection was cleared
        assert initialized_manager._shared_connection is None


class TestGlobalPoolManager:
    """Test global pool manager improvements"""

    def test_singleton_pattern(self):
        """Test that GlobalPoolManager is a singleton"""
        manager1 = GlobalPoolManager()
        manager2 = GlobalPoolManager()
        assert manager1 is manager2

    @pytest.mark.asyncio
    async def test_pool_reuse(self):
        """Test that pools are reused for same database"""
        manager = get_global_pool_manager()
        
        with patch('erp.database.connection.pool.ConnectionPool') as mock_pool_class:
            mock_pool = AsyncMock()
            mock_pool._pool = MagicMock()
            mock_pool._pool._minsize = 2
            mock_pool._pool._maxsize = 5
            mock_pool_class.return_value = mock_pool

            # First call creates pool
            pool1 = await manager.get_pool("test_db")
            assert mock_pool.create_pool.call_count == 1

            # Second call reuses pool
            pool2 = await manager.get_pool("test_db")
            assert pool1 is pool2
            assert mock_pool.create_pool.call_count == 1

    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test pool health checking"""
        manager = get_global_pool_manager()
        
        # Mock a healthy pool
        mock_pool = AsyncMock()
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = 1
        mock_pool.acquire_connection.return_value.__aenter__.return_value = mock_conn
        mock_pool.acquire_connection.return_value.__aexit__.return_value = None
        mock_pool.is_closed = False
        
        manager._pools["test_db"] = mock_pool
        
        health = await manager.health_check("test_db")
        assert health is True

    def test_pool_stats(self):
        """Test pool statistics collection"""
        manager = get_global_pool_manager()
        
        # Mock pool with stats
        mock_pool = MagicMock()
        mock_pool._pool = MagicMock()
        mock_pool._pool._minsize = 2
        mock_pool._pool._maxsize = 5
        mock_pool._pool.get_size.return_value = 3
        mock_pool.is_closed = False
        
        manager._pools["test_db"] = mock_pool
        
        stats = manager.get_pool_stats()
        assert stats["total_pools"] == 1
        assert "test_db" in stats["pools"]
        assert stats["pools"]["test_db"]["min_size"] == 2
        assert stats["pools"]["test_db"]["max_size"] == 5


class TestPoolMonitoring:
    """Test pool monitoring functionality"""

    @pytest.fixture
    def monitor(self):
        """Pool monitor instance"""
        return PoolMonitor()

    @pytest.mark.asyncio
    async def test_monitoring_lifecycle(self, monitor):
        """Test monitoring start/stop lifecycle"""
        assert not monitor._monitoring_active

        # Start monitoring
        await monitor.start_monitoring(interval=1)
        assert monitor._monitoring_active
        assert monitor._monitor_task is not None

        # Stop monitoring
        await monitor.stop_monitoring()
        assert not monitor._monitoring_active

    @pytest.mark.asyncio
    async def test_metrics_collection(self, monitor):
        """Test metrics collection"""
        with patch('erp.database.monitoring.pool_monitor.get_global_pool_manager') as mock_manager:
            mock_pool_manager = MagicMock()
            mock_pool_manager.get_pool_stats.return_value = {
                "total_pools": 2,
                "total_max_connections": 10,
                "pools": {"test_db": {"min_size": 2, "max_size": 5}}
            }
            mock_manager.return_value = mock_pool_manager

            await monitor._collect_metrics()
            
            assert len(monitor._metrics) == 1
            latest_metrics = list(monitor._metrics.values())[0]
            assert latest_metrics["total_pools"] == 2

    def test_issue_diagnosis(self, monitor):
        """Test pool issue diagnosis"""
        # Mock high connection count scenario
        with patch.object(monitor, 'get_current_metrics') as mock_metrics:
            with patch.object(monitor, 'get_health_status') as mock_health:
                mock_metrics.return_value = {
                    "total_pools": 15,
                    "total_max_connections": 150
                }
                mock_health.return_value = {"test_db": False}

                issues = asyncio.run(monitor.diagnose_issues())
                
                # Should detect multiple issues
                issue_types = [issue["type"] for issue in issues]
                assert "high_pool_count" in issue_types
                assert "high_connection_count" in issue_types
                assert "unhealthy_pools" in issue_types


class TestDatabaseRegistry:
    """Test database registry improvements"""

    @pytest.mark.asyncio
    async def test_registry_initialization_failure_handling(self):
        """Test that registry handles initialization failures properly"""
        with patch('erp.database.registry.database_registry.DatabaseManager') as mock_manager_class:
            mock_manager = AsyncMock()
            mock_manager.ensure_initialized.side_effect = Exception("Connection failed")
            mock_manager_class.return_value = mock_manager

            with pytest.raises(Exception, match="Connection failed"):
                await DatabaseRegistry.get_database("test_db")

            # Verify failed manager was removed from registry
            assert "test_db" not in DatabaseRegistry._databases


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
