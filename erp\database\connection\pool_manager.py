"""
Global Database Connection Pool Manager

This module provides a centralized connection pool manager that can be shared
across multiple processes to prevent connection pool exhaustion.
"""

import asyncio
from typing import Any, Dict, Optional
from contextlib import asynccontextmanager

import asyncpg

from ...config import config
from ...logging import get_logger
from .pool import ConnectionPool


class GlobalPoolManager:
    """
    Global connection pool manager that prevents duplicate pool creation
    and manages connection limits across the entire application.
    """

    _instance: Optional['GlobalPoolManager'] = None
    _lock = asyncio.Lock()
    _pools: Dict[str, ConnectionPool] = {}
    _logger = get_logger(__name__)
    _total_connections = 0  # Track total connections across all pools
    _max_total_connections = 100  # Global connection limit

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    async def get_pool(cls, db_name: str) -> ConnectionPool:
        """
        Get or create a connection pool for the specified database.

        This method ensures only one pool per database exists globally,
        preventing connection exhaustion.
        """
        async with cls._lock:
            if db_name not in cls._pools:
                cls._logger.debug("Creating new global pool for database: %s", db_name)

                # Check if we're approaching connection limits
                pool_count = len(cls._pools)
                if pool_count >= 10:  # Warn if too many pools
                    cls._logger.warning(
                        "High number of database pools (%d). "
                        "Consider connection optimization.",
                        pool_count
                    )

                pool = ConnectionPool(db_name)
                await pool.create_pool()
                cls._pools[db_name] = pool

                # Update connection tracking
                pool_config = pool._pool._minsize, pool._pool._maxsize
                cls._total_connections += pool_config[1]  # Add max connections

                cls._logger.info(
                    "Global pool created for database: %s "
                    "(pools: %d, total max connections: %d)",
                    db_name, len(cls._pools), cls._total_connections
                )
            else:
                cls._logger.debug(
                    "Reusing existing global pool for database: %s", db_name
                )

            return cls._pools[db_name]

    @classmethod
    async def close_pool(cls, db_name: str):
        """Close and remove a specific database pool"""
        async with cls._lock:
            if db_name in cls._pools:
                await cls._pools[db_name].close_pool()
                del cls._pools[db_name]
                cls._logger.info("Global pool closed for database: %s", db_name)

    @classmethod
    async def close_all_pools(cls):
        """Close all database pools"""
        async with cls._lock:
            for db_name, pool in cls._pools.items():
                try:
                    await pool.close_pool()
                    cls._logger.info("Global pool closed for database: %s", db_name)
                except Exception as e:
                    cls._logger.error("Error closing pool for %s: %s", db_name, e)

            cls._pools.clear()
            cls._logger.info("All global pools closed")

    @classmethod
    def get_pool_count(cls) -> int:
        """Get the number of active pools"""
        return len(cls._pools)

    @classmethod
    def get_active_databases(cls) -> list:
        """Get list of databases with active pools"""
        return list(cls._pools.keys())

    @classmethod
    async def health_check(cls, db_name: str) -> bool:
        """Check if a specific pool is healthy"""
        if db_name not in cls._pools:
            return False

        pool = cls._pools[db_name]
        if pool.is_closed:
            return False

        try:
            # Test connection with a simple query
            async with pool.acquire_connection() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            cls._logger.warning("Health check failed for %s: %s", db_name, e)
            return False

    @classmethod
    async def health_check_all(cls) -> Dict[str, bool]:
        """Check health of all pools"""
        results = {}
        for db_name in cls._pools.keys():
            results[db_name] = await cls.health_check(db_name)
        return results

    @classmethod
    async def recover_pool(cls, db_name: str) -> bool:
        """Attempt to recover a failed pool"""
        try:
            cls._logger.info("Attempting to recover pool for database: %s", db_name)

            # Close existing pool if it exists
            if db_name in cls._pools:
                await cls.close_pool(db_name)

            # Create new pool
            pool = ConnectionPool(db_name)
            await pool.create_pool()
            cls._pools[db_name] = pool

            # Test the new pool
            if await cls.health_check(db_name):
                cls._logger.info("Successfully recovered pool for database: %s", db_name)
                return True
            else:
                cls._logger.error("Failed to recover pool for database: %s", db_name)
                return False

        except Exception as e:
            cls._logger.error("Error recovering pool for %s: %s", db_name, e)
            return False

    @classmethod
    def get_pool_stats(cls) -> Dict[str, Any]:
        """Get statistics about all pools"""
        stats = {
            "total_pools": len(cls._pools),
            "total_max_connections": cls._total_connections,
            "pools": {}
        }

        for db_name, pool in cls._pools.items():
            if pool._pool:
                stats["pools"][db_name] = {
                    "min_size": pool._pool._minsize,
                    "max_size": pool._pool._maxsize,
                    "current_size": pool._pool.get_size(),
                    "is_closed": pool.is_closed
                }

        return stats


# Global instance
_global_pool_manager = None


def get_global_pool_manager() -> GlobalPoolManager:
    """Get the global pool manager instance"""
    global _global_pool_manager
    if _global_pool_manager is None:
        _global_pool_manager = GlobalPoolManager()
    return _global_pool_manager
