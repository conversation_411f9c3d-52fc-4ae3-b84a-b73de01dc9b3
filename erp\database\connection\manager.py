"""
Database manager that coordinates all database operations
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

import asyncpg

from ..operations.crud import CRUDOperations
from ..operations.schema import SchemaOperations
from .pool import ConnectionPool
from .pool_manager import get_global_pool_manager
from .sql_logger import <PERSON><PERSON><PERSON>og<PERSON>
from .transactions import TransactionManager


class DatabaseManager:
    """Database manager with connection pooling and modular operations"""

    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name
        self._shared_connection = None  # For sharing connection with cursor
        self._pool = None  # Will be set by create_pool()
        self._pool_initialized = False  # Track pool initialization state
        self._initialization_lock = asyncio.Lock()  # Prevent concurrent initialization

        # Initialize components (pool will be set later)
        self.sql_logger = SQLLogger(self.db_name)
        # Note: transactions, crud, and schema will be initialized after pool creation

    def set_shared_connection(self, connection):
        """Set a shared connection to use instead of pool connections"""
        self._shared_connection = connection

    def clear_shared_connection(self):
        """Clear the shared connection"""
        self._shared_connection = None

    async def create_pool(self):
        """Create connection pool using global pool manager"""
        async with self._initialization_lock:
            if self._pool_initialized:
                return  # Already initialized

            try:
                global_manager = get_global_pool_manager()
                self._pool = await global_manager.get_pool(self.db_name)

                # Initialize components that depend on the pool
                self.transactions = TransactionManager(self._pool)
                self.crud = CRUDOperations(self._pool, self.sql_logger)
                self.schema = SchemaOperations(self._pool, self.sql_logger)

                self._pool_initialized = True

            except Exception as e:
                # Reset state on failure
                self._pool = None
                self._pool_initialized = False
                raise

    async def close_pool(self):
        """Close connection pool via global pool manager"""
        async with self._initialization_lock:
            if self._pool is not None:
                global_manager = get_global_pool_manager()
                await global_manager.close_pool(self.db_name)
                self._pool = None
                self._pool_initialized = False

    async def ensure_initialized(self):
        """Ensure pool is initialized - call this once at startup"""
        if not self._pool_initialized:
            await self.create_pool()

    def _check_initialized(self):
        """Check if pool is initialized, raise error if not"""
        if not self._pool_initialized:
            raise RuntimeError(
                f"DatabaseManager for {self.db_name} not initialized. "
                "Call ensure_initialized() first."
            )

    # Delegate CRUD operations
    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        if self._shared_connection:
            return await self._shared_connection.execute(query, *args)
        self._check_initialized()
        return await self.crud.execute(query, *args)

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        if self._shared_connection:
            return await self._shared_connection.fetch(query, *args)
        self._check_initialized()
        return await self.crud.fetch(query, *args)

    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch single row"""
        if self._shared_connection:
            return await self._shared_connection.fetchrow(query, *args)
        self._check_initialized()
        return await self.crud.fetchrow(query, *args)

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        if self._shared_connection:
            return await self._shared_connection.fetchval(query, *args)
        self._check_initialized()
        return await self.crud.fetchval(query, *args)

    async def insert(self, table: str, data: Dict[str, Any]) -> Optional[str]:
        """Insert a record and return the ID"""
        self._check_initialized()
        return await self.crud.insert(table, data)

    async def update(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a record"""
        self._check_initialized()
        return await self.crud.update(table, record_id, data)

    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        self._check_initialized()
        return await self.crud.delete(table, record_id)

    async def exists(self, table: str, record_id: str) -> bool:
        """Check if a record exists"""
        self._check_initialized()
        return await self.crud.exists(table, record_id)

    async def count(
        self, table: str, where_clause: str = "", params: List = None
    ) -> int:
        """Count records in table"""
        self._check_initialized()
        return await self.crud.count(table, where_clause, params)

    # Delegate schema operations
    async def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create a table with specified columns"""
        self._check_initialized()
        await self.schema.create_table(table_name, columns)

    async def drop_table(self, table_name: str):
        """Drop a table"""
        self._check_initialized()
        await self.schema.drop_table(table_name)

    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        self._check_initialized()
        return await self.schema.table_exists(table_name)

    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table"""
        self._check_initialized()
        return await self.schema.get_table_columns(table_name)

    # Delegate transaction operations
    async def begin_transaction(self):
        """Begin a transaction"""
        self._check_initialized()
        return await self.transactions.begin_transaction()

    async def commit_transaction(self):
        """Commit current transaction"""
        self._check_initialized()
        return await self.transactions.commit_transaction()

    async def rollback_transaction(self):
        """Rollback current transaction"""
        self._check_initialized()
        return await self.transactions.rollback_transaction()

    # Direct access to components for advanced usage
    @property
    def acquire_connection(self):
        """Get connection context manager with initialization check"""
        @asynccontextmanager
        async def _acquire_with_check():
            self._check_initialized()
            async with self._pool.acquire_connection() as conn:
                yield conn

        return _acquire_with_check

    # Batch operations for efficiency
    async def batch_execute(self, queries: List[str]) -> List[str]:
        """Execute multiple queries in a single connection for efficiency"""
        self._check_initialized()
        results = []
        async with self._pool.acquire_connection() as conn:
            for query in queries:
                result = await conn.execute(query)
                results.append(result)
        return results

    async def batch_insert(self, table: str, records: List[Dict[str, Any]]) -> List[Optional[str]]:
        """Insert multiple records efficiently using a single connection"""
        self._check_initialized()
        results = []
        async with self._pool.acquire_connection() as conn:
            for record in records:
                result = await self.crud._insert_with_connection(conn, table, record)
                results.append(result)
        return results

    async def transaction_context(self):
        """Context manager for database transactions with connection reuse"""
        @asynccontextmanager
        async def _transaction():
            self._check_initialized()
            async with self._pool.acquire_connection() as conn:
                async with conn.transaction():
                    # Set shared connection for transaction scope
                    old_connection = self._shared_connection
                    self.set_shared_connection(conn)
                    try:
                        yield self
                    finally:
                        # Restore previous connection state
                        self.clear_shared_connection()
                        if old_connection:
                            self.set_shared_connection(old_connection)

        return _transaction()

    # Pool health and monitoring
    async def health_check(self) -> bool:
        """Check if this database manager's pool is healthy"""
        if not self._pool_initialized:
            return False

        try:
            async with self._pool.acquire_connection() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception:
            return False

    def get_pool_stats(self) -> Dict[str, Any]:
        """Get pool statistics for this database"""
        if not self._pool_initialized or not self._pool._pool:
            return {"status": "not_initialized"}

        pool = self._pool._pool
        return {
            "status": "initialized",
            "min_size": pool._minsize,
            "max_size": pool._maxsize,
            "current_size": pool.get_size(),
            "is_closed": self._pool.is_closed
        }
