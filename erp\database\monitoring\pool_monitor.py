"""
Database Pool Monitoring and Metrics

This module provides monitoring capabilities for database connection pools,
including metrics collection, health monitoring, and performance tracking.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from ...logging import get_logger
from ..connection.pool_manager import get_global_pool_manager


class PoolMonitor:
    """Monitor database connection pools for health and performance"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self._metrics = {}
        self._health_history = {}
        self._monitoring_active = False
        self._monitor_task = None

    async def start_monitoring(self, interval: int = 60):
        """Start continuous pool monitoring"""
        if self._monitoring_active:
            self.logger.warning("Pool monitoring already active")
            return

        self._monitoring_active = True
        self._monitor_task = asyncio.create_task(self._monitor_loop(interval))
        self.logger.info("Pool monitoring started with %d second interval", interval)

    async def stop_monitoring(self):
        """Stop pool monitoring"""
        if not self._monitoring_active:
            return

        self._monitoring_active = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        self.logger.info("Pool monitoring stopped")

    async def _monitor_loop(self, interval: int):
        """Main monitoring loop"""
        while self._monitoring_active:
            try:
                await self._collect_metrics()
                await self._check_health()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in monitoring loop: %s", e)
                await asyncio.sleep(interval)

    async def _collect_metrics(self):
        """Collect pool metrics"""
        timestamp = datetime.now()
        pool_manager = get_global_pool_manager()
        
        # Get overall stats
        stats = pool_manager.get_pool_stats()
        
        # Store metrics with timestamp
        self._metrics[timestamp] = {
            "total_pools": stats["total_pools"],
            "total_max_connections": stats["total_max_connections"],
            "pools": stats["pools"]
        }
        
        # Clean old metrics (keep last 24 hours)
        cutoff = timestamp - timedelta(hours=24)
        self._metrics = {
            ts: metrics for ts, metrics in self._metrics.items() 
            if ts > cutoff
        }

    async def _check_health(self):
        """Check health of all pools"""
        timestamp = datetime.now()
        pool_manager = get_global_pool_manager()
        
        health_results = await pool_manager.health_check_all()
        
        # Store health history
        self._health_history[timestamp] = health_results
        
        # Clean old health data (keep last 24 hours)
        cutoff = timestamp - timedelta(hours=24)
        self._health_history = {
            ts: health for ts, health in self._health_history.items() 
            if ts > cutoff
        }
        
        # Log unhealthy pools
        unhealthy = [db for db, healthy in health_results.items() if not healthy]
        if unhealthy:
            self.logger.warning("Unhealthy pools detected: %s", unhealthy)

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current pool metrics"""
        pool_manager = get_global_pool_manager()
        return pool_manager.get_pool_stats()

    def get_health_status(self) -> Dict[str, bool]:
        """Get current health status of all pools"""
        if not self._health_history:
            return {}
        
        # Return most recent health check
        latest_timestamp = max(self._health_history.keys())
        return self._health_history[latest_timestamp]

    def get_metrics_history(self, hours: int = 1) -> Dict[datetime, Dict[str, Any]]:
        """Get metrics history for specified hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return {
            ts: metrics for ts, metrics in self._metrics.items() 
            if ts > cutoff
        }

    def get_health_history(self, hours: int = 1) -> Dict[datetime, Dict[str, bool]]:
        """Get health history for specified hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return {
            ts: health for ts, health in self._health_history.items() 
            if ts > cutoff
        }

    def get_pool_summary(self) -> Dict[str, Any]:
        """Get a summary of pool status and metrics"""
        current_metrics = self.get_current_metrics()
        health_status = self.get_health_status()
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "monitoring_active": self._monitoring_active,
            "total_pools": current_metrics.get("total_pools", 0),
            "total_max_connections": current_metrics.get("total_max_connections", 0),
            "healthy_pools": sum(1 for healthy in health_status.values() if healthy),
            "unhealthy_pools": sum(1 for healthy in health_status.values() if not healthy),
            "pools": {}
        }
        
        # Add per-pool details
        for db_name, pool_stats in current_metrics.get("pools", {}).items():
            summary["pools"][db_name] = {
                "healthy": health_status.get(db_name, False),
                "min_size": pool_stats.get("min_size", 0),
                "max_size": pool_stats.get("max_size", 0),
                "current_size": pool_stats.get("current_size", 0),
                "is_closed": pool_stats.get("is_closed", True)
            }
        
        return summary

    async def diagnose_issues(self) -> List[Dict[str, Any]]:
        """Diagnose potential pool issues"""
        issues = []
        current_metrics = self.get_current_metrics()
        health_status = self.get_health_status()
        
        # Check for too many pools
        total_pools = current_metrics.get("total_pools", 0)
        if total_pools > 10:
            issues.append({
                "type": "high_pool_count",
                "severity": "warning",
                "message": f"High number of pools ({total_pools}). Consider connection optimization.",
                "recommendation": "Review database usage patterns and consider connection pooling optimization."
            })
        
        # Check for high connection usage
        total_connections = current_metrics.get("total_max_connections", 0)
        if total_connections > 100:
            issues.append({
                "type": "high_connection_count",
                "severity": "warning", 
                "message": f"High total max connections ({total_connections}). May exceed PostgreSQL limits.",
                "recommendation": "Reduce pool sizes or optimize connection usage patterns."
            })
        
        # Check for unhealthy pools
        unhealthy_pools = [db for db, healthy in health_status.items() if not healthy]
        if unhealthy_pools:
            issues.append({
                "type": "unhealthy_pools",
                "severity": "error",
                "message": f"Unhealthy pools detected: {unhealthy_pools}",
                "recommendation": "Check database connectivity and pool configuration."
            })
        
        return issues


# Global monitor instance
_pool_monitor = None


def get_pool_monitor() -> PoolMonitor:
    """Get the global pool monitor instance"""
    global _pool_monitor
    if _pool_monitor is None:
        _pool_monitor = PoolMonitor()
    return _pool_monitor
